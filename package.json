{"name": "visual-dashboard", "version": "0.1.0", "private": true, "homepage": "./", "dependencies": {"@ant-design/charts": "^1.0.18", "@ant-design/icons": "^4.5.0", "@craco/craco": "^6.1.1", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "antd": "^4.13.0", "axios": "^0.21.1", "craco-less": "^1.17.1", "laravel-echo": "^1.17.1", "pusher-js": "^8.4.0-rc2", "react": "^17.0.1", "react-dom": "^17.0.1", "react-fast-marquee": "^1.3.5", "react-highlight-words": "^0.17.0", "react-query": "^3.12.0", "react-scripts": "4.0.3", "web-vitals": "^1.0.1"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}