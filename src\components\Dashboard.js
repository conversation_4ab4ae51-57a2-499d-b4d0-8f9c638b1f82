import React, { useEffect, useState } from 'react'
import { Card, Divider, Layout, Typography } from 'antd'
import { getDashboardData, getQueues } from '../config/queries';
import apiClient from '../config/apiClient';
import { useQuery } from 'react-query';

const { Content } = Layout;

const cardContainer = {
    display: 'grid',
    gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
    gridColumnGap: '20px',
    gridRowGap: '10px',
    marginBottom: '20px',
}

const Dashboard = ({ queue, setQueue }) => {

    const {Content} = Layout
    const [queues, setQueues] = useState([])
    const [showModal, setShowModal] = useState(false)

    useEffect(() => {
        getQueues()
          .then(r => setQueues(r.data))
    }, [])

    const query = useQuery(['dashboard', queue], getDashboardData, {
        refetchOnWindowFocus: false,
        refetchOnReconnect: false,
        refetchOnMount: false,
        refetchInterval: 1000
    })

    const error = (
        <div style={{ color: '#000' }}>Sorry, something went wrong. Try adding self-signed SSL certificate <a href={process.env.REACT_APP_baseURL} rel="noreferrer" target="_blank">{process.env.REACT_APP_baseURL}</a></div>
    )

    const handleChange = value => {
        setQueue(value)
    }

    return (
        <Content style={{ padding: '20px 20px' }}>
            <Typography.Title
                level={2}
                style={{
                    marginTop: '10px',
                    marginBottom: '15px',
                    textAlign: 'center',
                    color: 'gray'
                }}
                >
                Gerry's International Contact Center | Dashboard
            </Typography.Title>
            <Divider/>
            <div style={cardContainer}>
                <Card style={{ backgroundColor: '#00a437', borderRadius: '10px' }}>
                    <Typography.Title
                        level={1}
                        style={{
                            marginTop: '10px',
                            color: '#fff'
                        }}
                    >
                        {query.data?.queue_params.totalcalls.toString()}
                    </Typography.Title>
                    <Typography.Title
                        level={3}
                        style={{
                            color: '#fff',
                            margin: 0
                        }}
                    >
                        GOS
                    </Typography.Title>
                </Card>
                <Card style={{ backgroundColor: '#00a437', borderRadius: '10px' }}>
                    <Typography.Title
                        level={1}
                        style={{
                            marginTop: '10px',
                            color: '#fff'
                        }}
                    >
                        90.05%
                    </Typography.Title>
                    <Typography.Title
                        level={3}
                        style={{
                            color: '#fff',
                            margin: 0
                        }}
                    >
                        ACR
                    </Typography.Title>
                </Card>
                <Card style={{ backgroundColor: '#00a437', borderRadius: '10px' }}>
                    <Typography.Title
                        level={1}
                        style={{
                            marginTop: '10px',
                            color: '#fff'
                        }}
                    >
                        {query.data?.queue_params.totalcalls.toString()}
                    </Typography.Title>
                    <Typography.Title
                        level={3}
                        style={{
                            color: '#fff',
                            margin: 0
                        }}
                    >
                        Total Calls Today
                    </Typography.Title>
                </Card>
                <Card style={{ backgroundColor: '#00a437', borderRadius: '10px' }}>
                    <Typography.Title
                        level={1}
                        style={{
                            marginTop: '10px',
                            color: '#fff'
                        }}
                    >
                        90.05%
                    </Typography.Title>
                    <Typography.Title
                        level={3}
                        style={{
                            color: '#fff',
                            margin: 0
                        }}
                    >
                        Ans Calls
                    </Typography.Title>
                </Card>
                <Card style={{ backgroundColor: '#00a437', borderRadius: '10px' }}>
                    <Typography.Title
                        level={1}
                        style={{
                            marginTop: '10px',
                            color: '#fff'
                        }}
                    >
                        {query.data?.queue_params.abandoned}
                    </Typography.Title>
                    <Typography.Title
                        level={3}
                        style={{
                            color: '#fff',
                            margin: 0
                        }}
                    >
                        Call Abandoned
                    </Typography.Title>
                </Card>
            </div>
            <div style={cardContainer}>
                <Card style={{ backgroundColor: '#1da2c4', borderRadius: '10px' }}>
                    <Typography.Title
                        level={1}
                        style={{
                            marginTop: '10px',
                            color: '#fff'
                        }}
                    >
                        90.05%
                    </Typography.Title>
                    <Typography.Title
                        level={3}
                        style={{
                            color: '#fff',
                            margin: 0
                        }}
                    >
                        Agent Online
                    </Typography.Title>
                </Card>
                <Card style={{ backgroundColor: '#1da2c4', borderRadius: '10px' }}>
                    <Typography.Title
                        level={1}
                        style={{
                            marginTop: '10px',
                            color: '#fff'
                        }}
                    >
                        90.05%
                    </Typography.Title>
                    <Typography.Title
                        level={3}
                        style={{
                            color: '#fff',
                            margin: 0
                        }}
                    >
                        Available Agent
                    </Typography.Title>
                </Card>
                <Card style={{ backgroundColor: '#1da2c4', borderRadius: '10px' }}>
                    <Typography.Title
                        level={1}
                        style={{
                            marginTop: '10px',
                            color: '#fff'
                        }}
                    >
                        90.05%
                    </Typography.Title>
                    <Typography.Title
                        level={3}
                        style={{
                            color: '#fff',
                            margin: 0
                        }}
                    >
                        Active Call
                    </Typography.Title>
                </Card>
                <Card style={{ backgroundColor: '#1da2c4', borderRadius: '10px' }}>
                    <Typography.Title
                        level={1}
                        style={{
                            marginTop: '10px',
                            color: '#fff'
                        }}
                    >
                        90.05%
                    </Typography.Title>
                    <Typography.Title
                        level={3}
                        style={{
                            color: '#fff',
                            margin: 0
                        }}
                    >
                        Agent Break
                    </Typography.Title>
                </Card>
                <Card style={{ backgroundColor: '#1da2c4', borderRadius: '10px' }}>
                    <Typography.Title
                        level={1}
                        style={{
                            marginTop: '10px',
                            color: '#fff'
                        }}
                    >
                        90.05%
                    </Typography.Title>
                    <Typography.Title
                        level={3}
                        style={{
                            color: '#fff',
                            margin: 0
                        }}
                    >
                        Queue Call
                    </Typography.Title>
                </Card>
            </div>
            <div style={cardContainer}>
                <Card style={{ backgroundColor: '#e23c44', borderRadius: '10px' }}>
                    <Typography.Title
                        level={1}
                        style={{
                            marginTop: '10px',
                            color: '#fff'
                        }}
                    >
                        90.05%
                    </Typography.Title>
                    <Typography.Title
                        level={3}
                        style={{
                            color: '#fff',
                            margin: 0
                        }}
                    >
                        This Month GOS
                    </Typography.Title>
                </Card>
                <Card style={{ backgroundColor: '#e23c44', borderRadius: '10px' }}>
                    <Typography.Title
                        level={1}
                        style={{
                            marginTop: '10px',
                            color: '#fff'
                        }}
                    >
                        90.05%
                    </Typography.Title>
                    <Typography.Title
                        level={3}
                        style={{
                            color: '#fff',
                            margin: 0
                        }}
                    >
                        This Month ACR
                    </Typography.Title>
                </Card>
                <Card style={{ backgroundColor: '#e23c44', borderRadius: '10px' }}>
                    <Typography.Title
                        level={1}
                        style={{
                            marginTop: '10px',
                            color: '#fff'
                        }}
                    >
                        90.05%
                    </Typography.Title>
                    <Typography.Title
                        level={3}
                        style={{
                            color: '#fff',
                            margin: 0
                        }}
                    >
                        Our Month Total Call
                    </Typography.Title>
                </Card>
                <Card style={{ backgroundColor: '#e23c44', borderRadius: '10px' }}>
                    <Typography.Title
                        level={1}
                        style={{
                            marginTop: '10px',
                            color: '#fff'
                        }}
                    >
                        90.05%
                    </Typography.Title>
                    <Typography.Title
                        level={3}
                        style={{
                            color: '#fff',
                            margin: 0
                        }}
                    >
                        Our Month Total Ans
                    </Typography.Title>
                </Card>
                <Card style={{ backgroundColor: '#e23c44', borderRadius: '10px' }}>
                    <Typography.Title
                        level={1}
                        style={{
                            marginTop: '10px',
                            color: '#fff'
                        }}
                    >
                        90.05%
                    </Typography.Title>
                    <Typography.Title
                        level={3}
                        style={{
                            color: '#fff',
                            marginBottom: 0
                        }}
                    >
                        Our Month Total ABD
                    </Typography.Title>
                </Card>
            </div>
        </Content>
    )
}

export default Dashboard