import { <PERSON><PERSON>, <PERSON><PERSON>, Col, Layout, Modal, Result, Row, Select, Spin, Typography } from "antd";
import DashElem from "./DashElem";
import DemoBullet from "./DemoBullet";
import { useQuery } from "react-query";
import { getDashboardData, getOutboundDashboardData, getQueues } from "../config/queries"
import { useEffect, useState } from "react"
import { SwitcherFilled, SoundOutlined } from "@ant-design/icons";
import avgTalkTime from "../assets/avgTalkTime.png";
import answeredCalls from "../assets/answeredCalls.png";
import avgout from "../assets/avgout.png";
import highwait from "../assets/high-wait.png"
import totalAgents from "../assets/totalAgents.png";
import avgWaitTime from "../assets/avgWaitTime.png";
import totalPaused from "../assets/totalPaused.png";
import abandonedCalls from "../assets/abandonedCalls.png";
import highestWaitTime from "../assets/highestWaitTime.png";
import totalBusy from "../assets/totalBusy.png";
import callsInQueue from "../assets/callsInQueue.png";
import serviceLevel from "../assets/serviceLevel.png";
import totalIdle from "../assets/totalIdle.png";
import reallogo from "../assets/Contactplus-9 (1).png"
// import logo from "../assets/logo-no-background.png"
import cplogo from "../assets/cp.png"
import agentoncall from "../assets/call-icon-14.jpg";
import apiClient from "../config/apiClient";
import Marquee from 'react-fast-marquee';
import Pusher from 'pusher-js'
import axios from 'axios'
import initializeEcho from '../config/echo';


function LiveDashboard({ queue, setQueue, token, userId }) {

    const { Content } = Layout
    const [queues, setQueues] = useState([])
    const [showModal, setShowModal] = useState(false)
    const [tickerMsg, setTickerMsg] = useState('')
    const [echo, setEcho] = useState(null);


    useEffect(() => {
        if (token && !echo) {
            const instance = initializeEcho(token);
            setEcho(instance);
        }
    }, [token]);

    useEffect(() => {
        getQueues()
            .then(r => setQueues(r.data))

        apiClient.get('dashboard/ticker').then((res) => {
            if (res.data) setTickerMsg(res.data?.message)
        })
    }, [])


    useEffect(() => {
        getQueues()
            .then(r => setQueues(r.data))
    }, [])

    // const query = useQuery(['dashboard', queue], getDashboardData, {
    //     refetchOnWindowFocus: false,
    //     refetchOnReconnect: false,
    //     refetchOnMount: false,
    //     refetchInterval: 1000
    // })

    const [query, setQuery] = useState({
        isLoading: true,
        isSuccess: false,
        isError: false,
        data: [],
        error: null,
    });

    // useEffect(() => {
    //     axios.get(
    //         `${process.env.REACT_APP_baseURL}/dashboard`,
    //         {
    //             params: { queue: queue }
    //         }
    //     ).then((response) => {
    //         setQuery({
    //             isLoading: false,
    //             isSuccess: true,
    //             isError: false,
    //             data: response.data,
    //             error: null,
    //         });
    //     })
    //         .catch((error) => {
    //             console.error("Error fetching data:", error);
    //             setQuery({
    //                 isLoading: false,
    //                 isSuccess: false,
    //                 isError: true,
    //                 data: [],
    //                 error,
    //             });
    //         });
    // }, []);


    useEffect(() => {
        if (!echo || userId == null || !userId || queue == null || queue === "") return;

        console.log(`[Echo] Attempting to subscribe to: dashboard-data-queue-channel.${userId}.${queue}`);

        const channel = echo.private(`dashboard-data-queue-channel.${userId}.${queue}`);
        channel.listen(".dashboard.data.queue", (e) => {
            console.log(`[Echo] Event received: .dashboard.data.queue`);
            console.log(`[Echo] Payload:`, e);
            if (e.data?.data)
                setQuery((prev) => ({
                    ...prev,
                    isLoading: false,
                    isSuccess: true,
                    data: e.data.data || prev.data,
                }));
        })
            .error((error) => {
                console.error(`[Echo] Error subscribing to channel:`, error);
            });

        echo.connector.pusher.connection.bind('connected', () => {
            console.log('[Echo] Pusher connected');
        });

        echo.connector.pusher.connection.bind('disconnected', () => {
            console.warn('[Echo] Pusher disconnected');
        });

        echo.connector.pusher.connection.bind('error', (err) => {
            console.error('[Echo] Pusher connection error:', err);
        });

        echo.connector.pusher.connection.bind('state_change', (states) => {
            console.log('[Echo] Connection state change:', states);
        });

        // Cleanup subscription on component unmount
        return () => {
            console.log('[Echo] Unsubscribing from channel');
            channel.stopListening(".dashboard.data.queue");
            echo.leave(`private-dashboard-data-queue-channel.${userId}.${queue}`);
        };
    }, [echo, queue, userId]);

    useEffect(() => {
        if (query !== null) {
            // console.log("Dashboard Data:", query);
        }
    }, [query]);

    const [outbound, setOutbound] = useState(null);

    useEffect(() => {
        axios.get(`${process.env.REACT_APP_baseURL}/dashboard/outbound`).then((response) => {
            setOutbound(response);
        })
            .catch((error) => {
                console.error("Error fetching data:", error);
            });
    }, []);

    useEffect(() => {
        if (!echo || userId == null || !userId || queue == null || queue === "") return;

        const channel = echo.private(`dashboard-outbond-channel.${userId}.${queue}`);

        channel.listen(".dashboard.outbound", (e) => {
            if (e.data?.data)
                setQuery((prev) => ({
                    ...prev,
                    isLoading: false,
                    isSuccess: true,
                    data: e.data.data || prev.data,
                }));
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".dashboard.outbound");
        };
    }, [echo, queue, userId]);

    useEffect(() => {
        if (outbound !== null) {
            // console.log("Outbound Data:", outbound);
        }
    }, [outbound]);


    // const outbound = useQuery(['outbound'], getOutboundDashboardData, {
    //     refetchOnWindowFocus: false,
    //     refetchOnReconnect: false,
    //     refetchOnMount: false,
    //     refetchInterval: 1000
    // })

    const error = (
        <div style={{ color: '#000' }}>Sorry, something went wrong. Try adding self-signed SSL certificate <a href={process.env.REACT_APP_baseURL} rel="noreferrer" target="_blank">{process.env.REACT_APP_baseURL}</a></div>
    )

    const handleChange = value => {
        setQueue(value)
    }

    const safe = (val) => val?.toString() || "0";

    return (
        <>
            <Modal
                title="Switch Queue"
                visible={showModal}
                onOk={() => setShowModal(false)}
                onCancel={() => setShowModal(false)}
            >
                <Select onChange={handleChange} defaultValue={queue} style={{ width: 200 }}>
                    {queues.map(v => <Select.Option value={v} key={v}>{v}</Select.Option>)}
                </Select>
            </Modal>

            {query?.isError ?
                <Result
                    status="500"
                    title="500"
                    subTitle={error}
                    extra={<Button onClick={() => window.location.reload()} type="primary">Refresh</Button>}
                /> : query?.isSuccess &&
                <Content style={{ padding: '20px 20px' }}>

                    <Spin spinning={query?.isLoading}>
                        <Row gutter={[10]}>
                            {/* <div style={{ display: "flex", gap: '10px', alignItems: 'center' }}>
                                
                            </div> */}
                            <Col span={4} style={{ background: '#fffbe6', display: 'flex', marginBottom: '10px' }}>
                                <img src={reallogo} alt="logo" width={150} />

                            </Col>
                            <Col span={20} style={{ display: 'flex', background: '#fffbe6', borderRadius: '10px', alignItems: 'flex-end', marginBottom: '10px' }}>
                                <Alert
                                    banner
                                    style={{ height: '70px', background: "none", width: "100%" }}
                                    icon={<SoundOutlined />}
                                    // message={<Typography.Title level={3} style={{ margin: 0, marginTop: '-5px', color: '#f9b016' }}>Announcement</Typography.Title>}
                                    description={
                                        <Marquee pauseOnHover gradient={false}>
                                            <Typography.Title level={1} style={{ color: '#808080', marginTop: '-10px', overflow: 'hidden' }}>{tickerMsg}</Typography.Title>
                                        </Marquee>
                                    }
                                />
                            </Col>

                            <Col xs={12} lg={4}>
                                <DashElem
                                    hasColor={true}
                                    img={callsInQueue}
                                    type={"Inbound"}
                                    desc={"Calls In Queue"} data={query?.data?.queue_params?.calls.toString() || "0"} />
                                <DashElem type={"Inbound"} data={query?.data?.queue_params?.talktime || "0"}
                                    img={avgTalkTime} desc="Avg. Talk Time" />
                                <DashElem type={"Inbound"} data={query?.data?.queue_params2?.total.toString() || "0"}
                                    img={totalAgents}
                                    desc="Total Agents" />
                                <DashElem type={"Outbound"} data={outbound?.data?.total_dialed_calls.toString() || "0"} />
                            </Col>
                            <Col xs={12} lg={4}>
                                <DashElem
                                    type={"Inbound"}
                                    data={query?.data?.queue_params?.completed || "0"}
                                    img={answeredCalls}
                                    desc="Answered Calls" />
                                <DashElem type={"Inbound"} data={query?.data?.queue_params?.holdtime || "0"}
                                    img={avgWaitTime} desc="Avg. Wait Time" />
                                <DashElem type={"Inbound"} data={query?.data?.queue_params2?.paused.toString() || "0"}
                                    img={totalPaused}
                                    desc="Agents On Break" />
                                <DashElem data={outbound?.data?.total_answered_calls || "0"}
                                    img={answeredCalls}
                                    type={"Outbound"}
                                    desc="Answered Calls" />
                            </Col>
                            <Col xs={12} lg={4}>
                                <DashElem
                                    type={"Inbound"}
                                    data={query?.data?.queue_params?.abandoned || "0"}
                                    img={abandonedCalls} desc="Abandoned Calls" />
                                <DashElem data={query?.data?.queue_params2?.longestholdtime || "0"}
                                    type={"Inbound"}
                                    img={highwait}
                                    desc="Highest Wait Time" />
                                <DashElem
                                    type={"Inbound"}
                                    data={query?.data?.queue_params2?.busy.toString()}
                                    img={agentoncall} desc="Agents On Call" />
                                <DashElem type={"Outbound"} data={outbound?.data?.avg_call_duration}
                                    img={avgout} desc="Average Call Duration" />
                            </Col>
                            <Col xs={12} lg={4}>
                                <DashElem
                                    // hasColor={true}
                                    type={"Inbound"}
                                    data={query?.data?.queue_params?.totalcalls}
                                    desc="Total Incoming Calls" />
                                <DashElem
                                    type={"Inbound"}
                                    data={query?.data?.queue_params?.avgHandlingTime}
                                    img={avgWaitTime} desc="Average Handling Time" />
                                <DashElem
                                    type={"Inbound"}
                                    data={query?.data?.queue_params2?.idle.toString()}
                                    img={totalIdle} desc="Ready Agents" />
                                <DashElem
                                    type={"Outbound"}
                                    data={outbound?.data?.answered_rate}
                                    img={serviceLevel} desc="Answer Rate" />
                            </Col>
                            <Col xs={24} lg={8} >
                                <DemoBullet setShowModal={setShowModal} queue={queue} serviceLevel={query?.data?.queue_params?.servicelevelperf2} />
                                {/* <Button onClick={() => setShowModal(true)} icon={<SwitcherFilled />}>Selected Queue: {queue}</Button> */}
                            </Col>
                        </Row>

                        {/* <Button onClick={() => setShowModal(true)} icon={<SwitcherFilled />}>Selected Queue: {queue}</Button> */}
                    </Spin>
                </Content>
            }
        </>
    );
}

export default LiveDashboard;
