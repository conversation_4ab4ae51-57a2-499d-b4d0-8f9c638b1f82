import { useCallback, useEffect, useRef, useState } from 'react';
import './App.less';
import Main from "./components/Main";
import { QueryClient, QueryClientProvider } from "react-query";
import { ReactQueryDevtools } from "react-query/devtools";
import apiClient from './config/apiClient';
import { Spin } from 'antd';

const queryClient = new QueryClient();

function App() {
    const [loading, setLoading] = useState(true);
    const [token, setToken] = useState(() => sessionStorage.getItem('auth_token') || "");
    const [userId, setUserId] = useState(() => sessionStorage.getItem('id') || "");
    const hasSetLoading = useRef(false);

    const checkUserAuthentic = useCallback(async () => {
        try {
            const urlParams = new URLSearchParams(window.location.search);
            const authToken = urlParams.get('auth_token');

            if (authToken) {
                sessionStorage.setItem('auth_token', authToken);
                setToken(authToken);
            }

            const response = await apiClient.post('/check-user-authentic');

            if (response.data?.status) {
                const userId = response.data?.user?.id;
                sessionStorage.setItem('id', userId);
                setUserId(userId);

                if (!hasSetLoading.current) {
                    setLoading(false);
                    hasSetLoading.current = true;
                }
            } else {
                throw new Error("Not authenticated");
            }
        } catch (error) {
            console.error("Authentication check failed:", error.message);
        }
    }, []);

    useEffect(() => {
        const urlParams = new URLSearchParams(window.location.search);
        const authToken = urlParams.get('auth_token');

        if (!authToken && !sessionStorage.getItem('auth_token')) {
            window.location.href = `https://${process.env.REACT_APP_SOCKET_APP_URL}/admin`;
        } else {
            checkUserAuthentic();
            const interval = setInterval(checkUserAuthentic, 5000);
            return () => clearInterval(interval);
        }
    }, [checkUserAuthentic]);

    return (
        <QueryClientProvider client={queryClient}>
            {loading ? (
                <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
                    <Spin />
                </div>
            ) : (
                <Main setLoading={setLoading} token={token} userId={userId} />
            )}
            <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
    );
}

export default App;
