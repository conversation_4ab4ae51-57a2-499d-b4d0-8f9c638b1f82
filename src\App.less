@import '~antd/dist/antd.less';

.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: initial;
  border-radius: .25rem;
  box-shadow: 0 0.46875rem 2.1875rem rgb(8 10 37 / 3%), 0 0.9375rem 1.40625rem rgb(8 10 37 / 3%), 0 0.25rem 0.53125rem rgb(8 10 37 / 5%), 0 0.125rem 0.1875rem rgb(8 10 37 / 3%);
  border-width: 0;
  transition: all .2s;
}

.widget-chart {
  text-align: center;
  padding: 1rem;
  position: relative;
}

.icon-wrapper {
  width: 54px;
  height: 54px;
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  display: flex;
  align-content: center;
  align-items: center;
}

.icon-wrapper .icon-wrapper-bg {
  position: absolute;
  height: 100%;
  width: 100%;
  z-index: 3;
  opacity: .2;
}

.rounded {
  border-radius: .25rem!important;
}

.bg-focus {
  background-color: #444054!important;
}

.widget-chart .widget-numbers {
  font-weight: 700;
  font-size: 2.5rem;
  display: block;
  line-height: 1;
  margin: 1rem auto;
}

.widget-chart .widget-subheading {
  margin: -.5rem 0 0;
  display: block;
  opacity: .6;
}

.widget-chart .widget-numbers+.widget-chart-flex, .widget-chart .widget-numbers+.widget-description, .widget-chart .widget-numbers+.widget-subheading {
  margin-top: -.5rem;
}

.widget-chart .widget-description {
  margin: 1rem 0 0;
}

.text-danger {
  color: #d92550!important;
}

@keyframes blinkingText{
  0%{     color: #F44336;    }
  49%{    color: #F44336; }
  60%{    color: transparent; }
  99%{    color:transparent;  }
  100%{   color: #F44336;    }
}
