import { Button, Input, Layout, Spin } from "antd"
import { Table, Tag, Space } from 'antd'
import {
    CheckCircleOutlined,
    CheckCircleTwoTone,
    CloseCircleTwoTone,
    SearchOutlined,
    ThunderboltOutlined
} from "@ant-design/icons";
import { useQuery } from "react-query";
import { getAgentData, getAgentDataOutbound } from "../config/queries";
import { useEffect, useState } from "react"
import Highlighter from 'react-highlight-words';
import moment from 'moment';
import axios from 'axios';
import initializeEcho from '../config/echo';


// const cardStyle = { borderRadius: '6px', display: 'flex', flexWrap: 'wrap', justifyContent: 'center', width: '100%', gap: '10px', background: '#fff', marginBottom: '10px', padding: '15px' }
const cardStyle = { borderRadius: '6px', display: 'flex', flexWrap: 'wrap', justifyContent: 'space-between', width: '100%', gap: '10px', background: '#fff', marginBottom: '10px', padding: '15px' }

const tableWrapperStyle = {
    flex: 1, // Each table takes equal space
    minWidth: '45%', // Prevents them from squeezing too much
    overflowX: 'auto', // Enables horizontal scrolling if needed
    boxShadow: '2px 2px 4px 0px rgba(0,0,0,0.25)',
    padding: '10px',
    background: '#fff'
};


const LiveMonitoring = ({ queue, token, userId }) => {

    // const query = useQuery(['agentQuery', queue], getAgentData, {
    //     refetchOnWindowFocus: false,
    //     refetchOnReconnect: false,
    //     refetchOnMount: false,
    //     refetchInterval: 5000
    // })

    // const queryOutbound = useQuery(['agentQueryOutbound', queue], getAgentDataOutbound, {
    //     refetchOnWindowFocus: false,
    //     refetchOnReconnect: false,
    //     refetchOnMount: false,
    //     refetchInterval: 5000
    // })

    console.log(`LiveMonitoring token : ${token}`)
    console.log(`LiveMonitoring userId : ${userId}`)

    const [echo, setEcho] = useState(null);

    useEffect(() => {
        if (token && !echo) {
            const instance = initializeEcho(token);
            setEcho(instance);
        }
    }, [token]);

    const [query, setQuery] = useState({
        isLoading: true,
        isSuccess: false,
        data: [],
        error: null,
    });

    useEffect(() => {
        axios.get(
            `${process.env.REACT_APP_baseURL}/dashboard/agent`,
            {
                params: { queue: queue, userId: userId }
            }
        ).then((response) => {
            setQuery({
                isLoading: false,
                isSuccess: true,
                data: response.data,
                error: null,
            });
        })
            .catch((error) => {
                console.error("Error fetching data:", error);
                setQuery({
                    isLoading: false,
                    isSuccess: false,
                    data: [],
                    error,
                });
            });

        // Listen for WebSocket events
        // const channel = echo.channel("dashboard-agent-queue-channel");
        // channel.listen(".dashboard.agent.queue", (e) => {
        //     setQuery((prev) => ({
        //         ...prev,
        //         isLoading: false,
        //         isSuccess: true,
        //         data: e.data || prev.data,
        //     }));
        // });

        // // Cleanup subscription on component unmount
        // return () => {
        //     channel.stopListening(".dashboard.agent.queue");
        // };
    }, [queue]);

    useEffect(() => {
        if (!echo || userId == null || !userId || queue == null || queue === "") return;

        const channel = echo.private(`dashboard-agent-queue-channel.${userId}.${queue}`);

        channel.listen(".dashboard.agent.queue", (e) => {
            if (e.data?.data == null) return;
            setQuery((prev) => ({
                ...prev,
                isLoading: false,
                isSuccess: true,
                data: e.data.data || prev.data,
            }));
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".dashboard.agent.queue");
        };
    }, [echo, queue, userId]);

    useEffect(() => {
        if (query !== null) {
            // console.log("Agent-Queue Data:", query);
        }
    }, [query]);



    const [queryOutbound, setQueryOutbound] = useState(null);
    useEffect(() => {
        axios.get(`${process.env.REACT_APP_baseURL}/dashboard/outbound-agent`).then((response) => {
            setQueryOutbound(response);
        })
            .catch((error) => {
                console.error("Error fetching data:", error);
            });

        // Listen for WebSocket events
        // const channel = echo.channel("dashboard-outbond-agent-channel");
        // channel.listen(".dashboard.outbound.agent", (e) => {
        //     setQueryOutbound(e);
        // });

        // // Cleanup subscription on component unmount
        // return () => {
        //     channel.stopListening(".dashboard.outbound.agent");
        // };
    }, []);

    useEffect(() => {
        if (!echo || userId == null || !userId || queue == null || queue === "") return;

        const channel = echo.private(`dashboard-outbond-agent-channel.${userId}.${queue}`);

        channel.listen(".dashboard.outbound.agent", (e) => {
            if (e.data?.data == null) return;
            setQuery((prev) => ({
                ...prev,
                isLoading: false,
                isSuccess: true,
                data: e.data.data || prev.data,
            }));
        });

        // Cleanup subscription on component unmount
        return () => {
            channel.stopListening(".dashboard.outbound.agent");
        };
    }, [echo, queue, userId]);

    useEffect(() => {
        if (queryOutbound !== null) {
            // console.log("Outbound Agent Data:", queryOutbound.data);
        }
    }, [queryOutbound]);

    const blinkStyle = {
        animation: 'blinkingText 1.2s infinite',
        fontWeight: 'bold'
    }

    const getColumnSearchProps = dataIndex => ({
        filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
            <div style={{ padding: 8 }}>
                <Input
                    // ref={node => {
                    //     searchInput = node;
                    // }}
                    placeholder={`Search ${dataIndex}`}
                    value={selectedKeys[0]}
                    onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
                    onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
                    style={{ marginBottom: 8, display: 'block' }}
                />
                <Space>
                    <Button
                        type="primary"
                        onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
                        icon={<SearchOutlined />}
                        size="small"
                        style={{ width: 90 }}
                    >
                        Search
                    </Button>
                    <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
                        Reset
                    </Button>
                </Space>
            </div>
        ),
        filterIcon: filtered => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
        onFilter: (value, record) =>
            record[dataIndex]
                ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
                : '',
        onFilterDropdownVisibleChange: visible => {
            if (visible) {
                // setTimeout(() => searchInput.select(), 100);
            }
        },
        render: text =>
            query.data.searchedColumn === dataIndex ? (
                <Highlighter
                    highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
                    searchWords={[this.state.searchText]}
                    autoEscape
                    textToHighlight={text ? text.toString() : ''}
                />
            ) : (
                text
            ),
    });

    const handleReset = clearFilters => {
        clearFilters();
        // this.setState({ searchText: '' });
    };


    const handleSearch = (selectedKeys, confirm, dataIndex) => {
        confirm();
        // query.data({
        //     searchText: selectedKeys[0],
        //     searchedColumn: dataIndex,
        // });
    };

    const columns2 = [{
        title: 'ID',
        dataIndex: 'id',
        key: 'name',
        sorter: (a, b) => a.name.localeCompare(b.name),
        // ...getColumnSearchProps('name')
    },
    {
        title: 'Name',
        dataIndex: 'name',
        key: 'name',
        sorter: (a, b) => a.name.localeCompare(b.name),
        ...getColumnSearchProps('name')
    },

    {
        title: 'Total Dialed Call',
        dataIndex: 'total_call',
        key: 'total_call',
        sorter: (a, b) => a.total_call - b.total_call,
        ...getColumnSearchProps('total_call')
    },
    {
        title: 'Total Answered Calls',
        dataIndex: 'total_answered_calls',
        key: 'total_answered_calls',
        sorter: (a, b) => a.total_answered_calls - b.total_answered_calls,
        ...getColumnSearchProps('total_answered_calls')
    },
    {
        title: 'Last Call',
        dataIndex: 'last_dialed_call',
        key: 'last_dialed_call',
        sorter: (a, b) => moment(a.last_dialed_call).unix() - moment(b.last_dialed_call).unix(),
        ...getColumnSearchProps('last_dialed_call')
    }
    ]

    const columns = [
        // {
        //     title: 'ID',
        //     dataIndex: 'id',
        //     key: 'name',
        //     sorter: (a, b) => a.name.localeCompare(b.name),
        //     // ...getColumnSearchProps('name')
        // },
        {
            title: 'ID',
            dataIndex: 'agentId',
            key: 'agentId',
            sorter: (a, b) => a.agentId - b.agentId,
        },
        {
            title: 'Name',
            dataIndex: 'name',
            key: 'name',
            sorter: (a, b) => a.name.localeCompare(b.name),
            ...getColumnSearchProps('name')
        },
        {
            title: 'Calls Taken',
            dataIndex: 'callstaken',
            key: 'callstaken',
            // sorter: (a, b) => a.queue - b.queues,
            sorter: (a, b) => Number(a.callstaken) - Number(b.callstaken),
            ...getColumnSearchProps('callstaken')
        },
        {
            title: 'Last Call',
            dataIndex: 'lastcall',
            key: 'lastcall',
            // sorter: (a, b) => moment(a.lastcall).unix() - moment(b.lastcall).unix(),
            sorter: (a, b) => {
                const format = "DD-MM-YYYY hh:mm:ss A"; // Correct format for your data

                const timeA = moment(a.lastcall, format, true).isValid()
                    ? moment(a.lastcall, format).unix()
                    : 0;

                const timeB = moment(b.lastcall, format, true).isValid()
                    ? moment(b.lastcall, format).unix()
                    : 0;

                return timeA - timeB;
            },
            ...getColumnSearchProps('lastcall')
        },
        {
            title: 'Status',
            dataIndex: 'status',
            key: 'status',
            render: text => (text === "DEVICE_RINGING" ? <div style={blinkStyle}>{text}</div> : <div>{text}</div>),
            sorter: (a, b) => a.status.localeCompare(b.status),
            ...getColumnSearchProps('status')
        },
        // {
        //     title: 'Interface',
        //     dataIndex: 'stateinterface',
        //     key: 'stateinterface',
        //     sorter: (a, b) => a.stateinterface.localeCompare(b.stateinterface),
        //     ...getColumnSearchProps('stateinterface')
        // },
        // {
        //     title: 'Membership',
        //     dataIndex: 'membership',
        //     key: 'membership',
        //     sorter: (a, b) => a.membership.localeCompare(b.membership),
        //     ...getColumnSearchProps('membership')
        // },
        // {
        //     title: 'Penalty',
        //     dataIndex: 'penalty',
        //     key: 'penalty',
        //     sorter: (a, b) => a.penalty - b.penalty,
        //     ...getColumnSearchProps('penalty')
        // },
        // {
        //     title: 'Calls taken',
        //     dataIndex: 'callstaken',
        //     key: 'callstaken',
        //     sorter: (a, b) => a.callstaken - b.callstaken,
        //     ...getColumnSearchProps('callstaken')
        // },
        // {
        //     title: 'Last Call',
        //     dataIndex: 'lastcall',
        //     key: 'lastcall',
        //     sorter: (a, b) => moment(a.lastcall).unix() - moment(b.lastcall).unix(),
        //     ...getColumnSearchProps('lastcall')
        // },
        {
            title: 'Break Reason',
            dataIndex: 'pausedreason',
            key: 'pausedreason',
            // sorter: (a, b) => a.pausedreason.localeCompare(b.pausedreason),
            sorter: (a, b) => (a.pausedreason || '').localeCompare(b.pausedreason || ''),
            ...getColumnSearchProps('pausedreason')
        },
        // {
        //     title: 'Last Pause',
        //     dataIndex: 'lastpause',
        //     key: 'lastpause',
        //     sorter: (a, b) => moment(a.lastpause).unix() - moment(b.lastpause).unix(),
        //     ...getColumnSearchProps('lastpause')
        // },
        // {
        //     title: 'In Call',
        //     dataIndex: 'incall',
        //     key: 'incall',
        //     render: text => text == "1" ? <CheckCircleTwoTone style={{ fontSize: 20 }} twoToneColor="#52c41a" /> : <CloseCircleTwoTone style={{ fontSize: 20 }} twoToneColor="#f50" />
        // },
        // {
        //     title: 'Status',
        //     dataIndex: 'status',
        //     key: 'status',
        //     render: text => (text === "DEVICE_RINGING" ? <div style={blinkStyle}>{text}</div> : <div>{text}</div>),
        //     sorter: (a, b) => a.status.localeCompare(b.status),
        //     ...getColumnSearchProps('status')
        // },
        // {
        //     title: 'Paused',
        //     dataIndex: 'paused',
        //     key: 'paused',
        //     render: text => text == "1" ? <CheckCircleTwoTone style={{ fontSize: 20 }} twoToneColor="#52c41a" /> : <CloseCircleTwoTone style={{ fontSize: 20 }} twoToneColor="#f50" />
        // },
        // {
        //     title: 'Paused Reason',
        //     dataIndex: 'pausedreason',
        //     key: 'pausedreason',
        //     sorter: (a, b) => a.pausedreason.localeCompare(b.pausedreason),
        //     ...getColumnSearchProps('pausedreason')
        // },
        // {
        //     title: 'Connected',
        //     dataIndex: 'connected',
        //     key: 'connected',
        // },
        // {
        //     title: 'App',
        //     dataIndex: 'application',
        //     key: 'application',
        // },
    ];


    const data = [
        {
            key: '1',
            queue: '100',
            name: 'John Brown',
            interface: 'PJSIP/1001',
            membership: 'dynamic',
            penalty: '0',
            callstaken: '0',
            lastcall: 'N/A',
            lastpause: 'N/A',
            incall: '0',
            status: '0',
            paused: '0',
            pausedreason: '0',
            wrapuptime: '0',
        },
    ]

    const title = () => (
        <h4>
            <ThunderboltOutlined /> Live Monitoring Inbound
        </h4>
    )
    const title2 = () => (
        <h4>
            <ThunderboltOutlined /> Live Monitoring Outbound
        </h4>
    )

    return (
        <Layout.Content style={{ padding: '0px 20px' }}>
            <Spin spinning={query.isLoading}>
                {query?.isSuccess ? <div style={cardStyle}>
                    {/* <div style={{ boxShadow: '2px 2px 4px 0px rgba(0,0,0,0.25)' }}> */}
                    <div style={tableWrapperStyle}>

                        {/* <Table rowKey={record => record.name} title={title} columns={columns} dataSource={query?.data || []} /> */}
                        <Table rowKey={record => record?.agentId} title={title} columns={columns} dataSource={query?.data || []} scroll={{ x: 'max-content' }} />
                    </div>
                    {/* <div style={{ boxShadow: '2px 2px 4px 0px rgba(0,0,0,0.25)' }}> */}
                    <div style={tableWrapperStyle}>
                        {/* <Table rowKey={record => record.name} title={title2} columns={columns2} dataSource={queryOutbound?.data || []} /> */}
                        <Table rowKey={record => record.agentId} title={title2} columns={columns2} dataSource={queryOutbound?.data || []} scroll={{ x: 'max-content' }} />
                    </div>
                </div> : 'Loading'}
            </Spin>
        </Layout.Content>
    )
}

export default LiveMonitoring