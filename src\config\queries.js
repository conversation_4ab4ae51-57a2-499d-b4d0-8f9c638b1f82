import apiClient from "./apiClient";
import { getAgent, getAgentOutbound, getDashboard, getOutbound, getQueue } from "./routes";

export const getDashboardData = ({ queryKey }) => {
    const [_, queue] = queryKey
    return apiClient.get(getDashboard, {
        params: {
            queue: queue
        }
    }).then(res => res.data)
}


export const getOutboundDashboardData = () => {
    // const [_, queue] = queryKey
    return apiClient.get(getOutbound).then(res => res.data)
}

export const getAgentData = ({ queryKey }) => {
    const [_, queue] = queryKey
    return apiClient.get(getAgent, {
        params: {
            queue: queue
        }
    }).then(res => res.data)
}


export const getAgentDataOutbound = () => {
    return apiClient.get(getAgentOutbound).then(res => res.data)
}
export const getQueues = () => apiClient.get(getQueue)