import { <PERSON><PERSON>, Col, Layout, Result, Row, Spin } from "antd";
import DashElem from "./DashElem";
import DemoBullet from "./DemoBullet";
import { useQuery } from "react-query";
import { getDashboardData } from "../config/queries"
import { useState, useEffect } from "react"
import LiveDashboard from "./LiveDashboard";
import LiveMonitoring from "./LiveMonitoring";

function Main({ token, userId, setLoading }) {
    const currentDate = new Date()
    const [date, setDate] = useState(currentDate.getFullYear())
    const [queue, setQueue] = useState('100')

    return (
        <Layout>
            <LiveDashboard setLoading={setLoading} queue={queue} setQueue={setQueue} token={token} userId={userId} />
            <LiveMonitoring setLoading={setLoading} queue={queue} token={token} userId={userId} />
            <Layout.Footer style={{ textAlign: 'center', backgroundColor: '#FFF', fontWeight: 'bold' }}>ContactPlus ©{date} | Powered by Telecard Ltd.</Layout.Footer>
        </Layout>
    );
}

export default Main;
