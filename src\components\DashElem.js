import { useState, useEffect } from "react"

const imgStyle = { textAlign: 'center', minWidth: '100px' }
const innerImg = { width: '40px', height: '40px' }
const dataStyle = { textAlign: 'center', fontSize: '20px', fontWeight: 'bold' }
const dataDesc = { textAlign: 'center' }

const DashElem = props => {

    const [color, setColor] = useState('#fff')
    const [fontColor, setFontColor] = useState('#000')

    useEffect(() => {
        let value = parseInt(props.data)
        if (props.hasColor && value >= 1) {
            setColor('#F44336')
            setFontColor('#fff')
        } else {
            setColor('#fff')
            setFontColor('#000')
        }
    }, [props.data, props.hasColor])

    const cardStyle = { borderRadius: '6px', boxShadow: '2px 2px 4px 0px rgba(0,0,0,0.25)', backgroundColor: color, marginBottom: '6px', padding: '10px', color: fontColor }

    return (
        <div style={cardStyle}>
            <span> {props.type}</span>
            <div style={imgStyle}>
                <img style={innerImg} src={props.img || "https://img.icons8.com/dusk/344/apple-phone.png"} alt={props.alt || ''} />
            </div>
            <div style={dataStyle}>
                {props.data === undefined ? "0" : props.data}
            </div>
            <div style={dataDesc}>
                {props.desc || 'Total Calls'}
            </div>
        </div>
    )
}

export default DashElem