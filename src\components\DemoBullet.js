import React from 'react';
import { Gauge } from '@ant-design/charts';
import { SwitcherFilled } from '@ant-design/icons';
import { Button } from 'antd';

const cardStyle = { minHeight: "100%", borderRadius: '6px', boxShadow: '2px 2px 4px 0px rgba(0,0,0,0.25)', display: 'flex', flexDirection: 'column', justifyContent: 'space-between', background: '#fff', marginBottom: '10px', padding: '24px' }

const DemoBullet = props => {

    const config = {
        percent: (props.serviceLevel / 100) || 0,
        type: 'meter',
        innerRadius: 0.75,
        range: {
            ticks: [0, 1 / 3, 2 / 3, 1],
            color: ['#F4664A', '#FAAD14', '#30BF78'],
        },
        axis: {
            label: {
                formatter(v) {
                    return Number(v) * 100;
                },
            },
            subTickLine: {
                count: 3,
            },
        },
        indicator: {
            pointer: {
                style: {
                    stroke: '#D0D0D0',
                },
            },
            pin: {
                style: {
                    stroke: '#D0D0D0',
                },
            },
        },
        statistic: {
            content: {
                style: {
                    fontSize: '36px',
                    lineHeight: '36px',
                },
            },
        },
    };

    return (
        <div style={cardStyle}>

            <Gauge {...config} />

            <Button onClick={() => props.setShowModal(true)} icon={<SwitcherFilled />}>Selected Queue: {props?.queue}</Button>
        </div>
    )
};
export default DemoBullet